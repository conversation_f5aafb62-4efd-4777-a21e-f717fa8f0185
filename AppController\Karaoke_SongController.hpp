
#ifndef KARAOKE_SONG_CONTROLLER_HPP_
#define KARAOKE_SONG_CONTROLLER_HPP_

#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "oatpp/orm/DbClient.hpp"
#include "DbClient_KaraokeSong.h" 

#include OATPP_CODEGEN_BEGIN(ApiController)

class KaraokeSongController : public oatpp::web::server::api::ApiController {
public:

  KaraokeSongController(const oatpp::String& routerPrefix, const std::shared_ptr<oatpp::data::mapping::ObjectMapper>& objectMapper, const std::shared_ptr<DbClient_KaraokeSong>& dbClient)
    : oatpp::web::server::api::ApiController(objectMapper, routerPrefix), mDbClient{ dbClient }
  {

  }

  ENDPOINT("GET", "/last_created/", getByLastCreated, QUERY(String, createdDateTime, "createdDateTime"), QUERY(UInt32, page_number, "page_number"))
  {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
      "Karaoke/song  :" + createdDateTime + "----" + std::to_string(page_number));
    response->putHeader("Controller", "Karaoke/song");
    return response;
  }

  ENDPOINT("GET", "/last_updated/", getByLastUpdated) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
      "{\"status\":\"healthy\",\"server\":\"MiWebServer/1.0.0\"}");
    response->putHeader("Controller", "Karaoke/song");
    response->putHeader("Content-Type", "application/json");
    return response;
  }
private:
  std::shared_ptr<DbClient_KaraokeSong> mDbClient;
};

#include OATPP_CODEGEN_END(ApiController)

#endif // KARAOKE_SONG_CONTROLLER_HPP_
