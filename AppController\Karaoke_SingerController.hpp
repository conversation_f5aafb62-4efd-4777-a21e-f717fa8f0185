
#ifndef KARAOKE_SINGER_CONTROLLER_HPP_
#define KARAOKE_SINGER_CONTROLLER_HPP_
#pragma once
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "DbClient_KaraokeSinger.h" 
#include "oatpp/encoding/Url.hpp"
#include OATPP_CODEGEN_BEGIN(ApiController)

class KaraokeSingerController : public oatpp::web::server::api::ApiController {
public:

  KaraokeSingerController(const oatpp::String& routerPrefix, const std::shared_ptr<oatpp::data::mapping::ObjectMapper>& objectMapper, const std::shared_ptr<DbClient_KaraokeSinger>& dbClient)
    : oatpp::web::server::api::ApiController(objectMapper, routerPrefix), mDbClient{ dbClient }
  {

  }

  ENDPOINT("GET", "/last_created/", getByLastCreated, QUERY(String, createdDateTime, "createdDateTime"), QUERY(Int64, offset, "offset"))
  {
     oatpp::String decodedDateTime2="feefgewfew";
    oatpp::String decodedDateTime = oatpp::encoding::Url::encode(decodedDateTime2);
   oatpp::String str= oatpp::utils::conversion::uint64ToStr(offset);
    //printf("GET %s %d \n", decodedDateTime->c_str(), (int)offset);
    auto QueryResult = mDbClient->getLastCreated(decodedDateTime, offset, LIMIT);
    if (QueryResult->isSuccess())
    {
      auto result = QueryResult->fetch<oatpp::Vector<oatpp::Fields<oatpp::Any>>>();
      std::for_each(result->cbegin(), result->cend(), [](const oatpp::Fields<oatpp::Any>& row) {
        printf("id %s %d \n", row["id"].getStoredType()->classId.name, (int)row["id"].retrieve<oatpp::Int64>());
        });
    }
// 
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
      "Karaoke/singer  :" + createdDateTime + "----" + std::to_string(offset));
    response->putHeader("Controller", "Karaoke/singer");
    return response;
  }

  ENDPOINT("GET", "/last_updated/", getByLastUpdated) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
      "{\"status\":\"healthy\",\"server\":\"MiWebServer/1.0.0\"}");
    response->putHeader("Controller", "Karaoke/singer");
    response->putHeader("Content-Type", "application/json");
    return response;
  }
private:
  std::shared_ptr<DbClient_KaraokeSinger> mDbClient;
  const static int LIMIT = 100;
};

#include OATPP_CODEGEN_END(ApiController)

#endif // KARAOKE_SINGER_CONTROLLER_HPP_
