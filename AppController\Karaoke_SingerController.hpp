
#ifndef KARAOKE_SINGER_CONTROLLER_HPP_
#define KARAOKE_SINGER_CONTROLLER_HPP_
#pragma once
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "DbClient_KaraokeSinger.h" 
#include "oatpp/encoding/Url.hpp"
#include OATPP_CODEGEN_BEGIN(ApiController)

class KaraokeSingerController : public oatpp::web::server::api::ApiController {
public:

  KaraokeSingerController(const oatpp::String& routerPrefix, const std::shared_ptr<oatpp::data::mapping::ObjectMapper>& objectMapper, const std::shared_ptr<DbClient_KaraokeSinger>& dbClient)
    : oatpp::web::server::api::ApiController(objectMapper, routerPrefix), mDbClient{ dbClient }
  {

  }

  ENDPOINT("GET", "/last_created/", getByLastCreated, QUERY(String, createdDateTime, "createdDateTime"), QUERY(Int64, offset, "offset"))
  {
    auto QueryResult = mDbClient->getLastCreated(oatpp::encoding::Url::decode(createdDateTime), offset, LIMIT);
    if (QueryResult->isSuccess())
    {
      auto result = QueryResult->fetch<oatpp::Vector<oatpp::Fields<oatpp::Any>>>();
       //oatpp::Vector<oatpp::Vector<oatpp::Any>> Table;

      std::for_each(result->cbegin(), result->cend(), [](const oatpp::Fields<oatpp::Any>& ROW) mutable
      {
                auto objectMapper = oatpp::parser::json::mapping::ObjectMapper::createShared();
        oatpp::String json = objectMapper->writeToString(ROW);

        // Output the JSON string
        printf("%s\n", json->c_str());
        //printf("id %s %d \n", ROW["id"].getStoredType()->classId.name, (int)ROW["id"].retrieve<oatpp::Int64>());
                  //   oatpp::Vector<oatpp::Any> values;
                  //   printf("id %s %s %s %s %s %s %d\n"
                  //   ,ROW["id"].getStoredType()->classId.name
                  //   ,ROW["fk_lan_id"].getStoredType()->classId.name
                  //   ,ROW["name"].getStoredType()->classId.name
                  //   ,ROW["name_abbr"].getStoredType()->classId.name
                  //    ,ROW["name_abbr_count"].getStoredType()->classId.name

                  //   ,(int)ROW->size()
                  // );
                  std::for_each(ROW->begin(), ROW->end(), [&](const std::pair<oatpp::String, oatpp::Any>& pair) {
                     printf("id %s \n",pair.second.getStoredType()->classId.name);
                  });
                  //Table->push_back(values);
        });
        //printf("%d\n", Table->size());
        
        // // Prepare field
        // oatpp::Vector<oatpp::String> Field = {"id", "message", "value"};

        // // Create result map
        // oatpp::UnorderedMap<oatpp::String, oatpp::Any> Result;
        // Result["field"] = Field;
        // Result["data"] = Table;

        // // Serialize to JSON
        // auto objectMapper = oatpp::parser::json::mapping::ObjectMapper::createShared();
        // oatpp::String json = objectMapper->writeToString(result);

        // // Output the JSON string
        // printf("%s\n", json->c_str());
    }
 
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
      "Karaoke/singer  :" + createdDateTime + "----" + std::to_string(offset));
    response->putHeader("Controller", "Karaoke/singer");
    return response;
  }

  ENDPOINT("GET", "/last_updated/", getByLastUpdated) {
    auto response = createResponse(oatpp::web::protocol::http::Status::CODE_200,
      "{\"status\":\"healthy\",\"server\":\"MiWebServer/1.0.0\"}");
    response->putHeader("Controller", "Karaoke/singer");
    response->putHeader("Content-Type", "application/json");
    return response;
  }
private:
  std::shared_ptr<DbClient_KaraokeSinger> mDbClient;
  const static int LIMIT = 100;
};

#include OATPP_CODEGEN_END(ApiController)

#endif // KARAOKE_SINGER_CONTROLLER_HPP_
