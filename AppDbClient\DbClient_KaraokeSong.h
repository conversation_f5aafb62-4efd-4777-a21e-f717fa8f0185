#ifndef DBCLIENT_KARAOKESONG_H
#define DBCLIENT_KARAOKESONG_H
#pragma once
#include "oatpp/orm/DbClient.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/Types.hpp"
#include "oatpp/core/macro/codegen.hpp"

#include OATPP_CODEGEN_BEGIN(DbClient)


class DbClient_KaraokeSong : public oatpp::orm::DbClient 
{
public:
    DbClient_KaraokeSong(const std::shared_ptr<oatpp::orm::Executor>& executor);
    ~DbClient_KaraokeSong();

private:

};
#include OATPP_CODEGEN_END(DbClient)
#endif