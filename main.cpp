#include "oatpp/network/Server.hpp"
#include "oatpp/network/tcp/server/ConnectionProvider.hpp"
#include "oatpp/network/Address.hpp"
#include "oatpp/web/server/HttpConnectionHandler.hpp"
#include "oatpp/web/server/HttpRouter.hpp"
#include "oatpp/web/server/api/ApiController.hpp"
#include "oatpp/web/protocol/http/Http.hpp"
#include "oatpp/parser/json/mapping/ObjectMapper.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/macro/codegen.hpp"
#include "oatpp/web/server/handler/ErrorHandler.hpp"
#include "oatpp/web/protocol/http/outgoing/BufferBody.hpp"
//EXTENSIONS INCLUDE
#include "oatpp-sqlite/Connection.hpp"
#include "oatpp-sqlite/ConnectionProvider.hpp"
#include "oatpp-sqlite/Executor.hpp"

//Include controllers and handlers
#include "CustomErrorHandler.hpp"
#include "Karaoke_SingerController.hpp"
#include "Karaoke_SongController.hpp"

//Include Database Clients
#include "AppDbClient/DbClient_KaraokeSong.h" 
#include "AppDbClient/DbClient_KaraokeSinger.h"

class AppComponent {
public:
  /////////////////////////////////////////////////////----HTTP ROUTER----//////////////////////////////////////////////////////
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, _HttpRouter)([] {
    return oatpp::web::server::HttpRouter::createShared();
  }());

  //////////////////////////////////////////////////----SERVER CONNECTION PROVIDER----////////////////////////////////////////
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, _ServerConnectionProvider)([] {
    return oatpp::network::tcp::server::ConnectionProvider::createShared({ "0.0.0.0", 8000, oatpp::network::Address::IP_4 });
  }());

  //////////////////////////////////////////////////----HTTP CONNECTION HANDLER----////////////////////////////////////////
  OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, _ServerConnectionHandler)([] {
    OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, router);
    auto connectionHandler = oatpp::web::server::HttpConnectionHandler::createShared(router);
    connectionHandler->setErrorHandler(std::make_shared<CustomErrorHandler>());
    return connectionHandler;
  }());

  //////////////////////////////////////////////////----DATABASE CONNECTION----////////////////////////////////////////
      OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::sqlite::ConnectionProvider>, _DbConnectionProvider)([] {
        #ifdef _WIN32 
        return std::make_shared<oatpp::sqlite::ConnectionProvider>("F:/Ubuntu_VirtualBox/Shared/MiWebApp/KaraokeSongDatabaseServer.db3");
        #else
        return std::make_shared<oatpp::sqlite::ConnectionProvider>("../KaraokeSongDatabaseServer.db3");
        #endif
    }());

    //////////////////////////////////////////////////----DATABASE CONNECTION POOL----////////////////////////////////////////
    OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::sqlite::ConnectionPool>, _DbConnectionPool)([] {
        OATPP_COMPONENT(std::shared_ptr<oatpp::sqlite::ConnectionProvider>, connectionProvider);
        return oatpp::sqlite::ConnectionPool::createShared(connectionProvider, 10 /* max-connections */, std::chrono::seconds(5) /* connection-ttl */);
    }());

    //////////////////////////////////////////////////----DATABASE EXECUTOR----////////////////////////////////////////
    OATPP_CREATE_COMPONENT(std::shared_ptr<oatpp::sqlite::Executor>, _DbExecutor)([] {
        OATPP_COMPONENT(std::shared_ptr<oatpp::sqlite::ConnectionPool>, connectionPool);
        return std::make_shared<oatpp::sqlite::Executor>(connectionPool);
    }());
};

void run() {
  AppComponent components;

  // Get components (httpRouter, serverConnectionProvider, serverConnectionHandler)
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpRouter>, CRouter);
  OATPP_COMPONENT(std::shared_ptr<oatpp::network::ServerConnectionProvider>, CConnectionProvider);
  OATPP_COMPONENT(std::shared_ptr<oatpp::web::server::HttpConnectionHandler>, CConnectionHandler);
  OATPP_COMPONENT(std::shared_ptr<oatpp::sqlite::Executor>, CDbExecutor);

  // Create Database client
   auto MDbClient_KaraokeSinger = std::make_shared<DbClient_KaraokeSinger>(CDbExecutor);
  auto MDbClient_KaraokeSong= std::make_shared<DbClient_KaraokeSong>(CDbExecutor);



  // Create and add controllers
  auto MObjectMapper = oatpp::parser::json::mapping::ObjectMapper::createShared();
  CRouter->addController(std::make_shared<KaraokeSingerController>("/karaoke/singer",MObjectMapper,MDbClient_KaraokeSinger));
  CRouter->addController(std::make_shared<KaraokeSongController>("/karaoke/song",MObjectMapper,MDbClient_KaraokeSong));


  // auto databaseController = std::make_shared<DatabaseController>(objectMapper, databaseService);
  // router->addController(databaseController);

  // Create and run server
  oatpp::network::Server server(CConnectionProvider, CConnectionHandler);
  OATPP_LOGI("MiWebServer", "Running on port %s...", CConnectionProvider->getProperty("port").toString()->c_str());
  OATPP_LOGI("MiWebServer", "Server identifier: MiWebServer/1.0.0");
  server.run();
}

int main(int argc, const char* argv[]) {
  oatpp::base::Environment::init();
  run();
  oatpp::base::Environment::destroy();
  return 0;
}


