/**
 * @file      CustomErrorHandler.hpp
 * <AUTHOR> Name (<EMAIL>)
 * @brief     Custom Error Handler for MiWebServer
 * @version   1.0.0
 * @date      24-06-2025
 * @copyright 2025, your company / association / school
 */

#ifndef CUSTOM_ERROR_HANDLER_HPP_
#define CUSTOM_ERROR_HANDLER_HPP_

#include "oatpp/web/server/handler/ErrorHandler.hpp"
#include "oatpp/web/protocol/http/outgoing/BufferBody.hpp"
#include "oatpp/web/protocol/http/Http.hpp"

/**
 * @brief Custom Error Handler to change server identifier
 */
class CustomErrorHandler : public oatpp::web::server::handler::ErrorHandler {
public:

  /**
   * @brief Handle error and return custom response
   * @param status HTTP status code
   * @param message Error message
   * @param headers Additional headers
   * @return Custom error response with MiWebServer identifier
   */
  std::shared_ptr<oatpp::web::protocol::http::outgoing::Response>
  handleError(const oatpp::web::protocol::http::Status& status,
              const oatpp::String& message,
              const Headers& headers) override {

    oatpp::data::stream::BufferOutputStream stream;
    stream << "server=MiWebServer/1.0.0\n";  // Custom server identifier
    stream << "code=" << status.code << "\n";
    stream << "description=" << status.description << "\n";
    stream << "message=" << message << "\n";

    auto response = oatpp::web::protocol::http::outgoing::Response::createShared(
      status,
      oatpp::web::protocol::http::outgoing::BufferBody::createShared(stream.toString())
    );

    response->putHeader("Server", "MiWebServer/1.0.0");  // Custom server header
    response->putHeader(oatpp::web::protocol::http::Header::CONNECTION,
                       oatpp::web::protocol::http::Header::Value::CONNECTION_CLOSE);

    for(const auto& pair : headers.getAll()) {
      response->putHeader_Unsafe(pair.first, pair.second);
    }

    return response;
  }
};

#endif // CUSTOM_ERROR_HANDLER_HPP_
