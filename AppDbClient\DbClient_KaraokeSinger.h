#ifndef DBCLIENT_KARAOKESINGER_H
#define DBCLIENT_KARAOKESINGER_H
#pragma once
#include "oatpp/orm/DbClient.hpp"
#include "oatpp/core/macro/component.hpp"
#include "oatpp/core/Types.hpp"
#include "oatpp/core/macro/codegen.hpp"

#include OATPP_CODEGEN_BEGIN(DbClient)

class DbClient_KaraokeSinger : public oatpp::orm::DbClient
{
public:
  DbClient_KaraokeSinger(const std::shared_ptr<oatpp::orm::Executor>& executor) : oatpp::orm::DbClient(executor)
  {

  }
  ~DbClient_KaraokeSinger() = default;

  QUERY(getLastCreated,
    "SELECT *  from singers where  created_at > :date_time AND id>:offset_id ORDER BY created_at ASC,id LIMIT :LIMIT",
    PARAM(oatpp::String, date_time),
    PARAM(oatpp::Int64, offset_id),
    PARAM(oatpp::UInt32, LIMIT));

  QUERY(getLastUpdated,
    "SELECT *  from singers where  created_at > :date_time AND id>:offset_id ORDER BY updated_at ASC,id LIMIT :LIMIT",
    PARAM(oatpp::String, date_time),
    PARAM(oatpp::Int64, offset_id),
    PARAM(oatpp::UInt32, LIMIT));

};
#include OATPP_CODEGEN_END(DbClient)
#endif